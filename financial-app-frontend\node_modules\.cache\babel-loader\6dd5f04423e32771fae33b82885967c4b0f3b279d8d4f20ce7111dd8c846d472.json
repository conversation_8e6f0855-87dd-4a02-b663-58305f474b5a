{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\fiancee\\\\financial-app-frontend\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport './styles.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 8,\n  columnNumber: 5\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/fiancee/financial-app-frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport './styles.css';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n    <React.StrictMode>\n        <App />\n    </React.StrictMode>\n);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACPL,OAAA,CAACJ,KAAK,CAACU,UAAU;EAAAC,QAAA,eACbP,OAAA,CAACF,GAAG;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACO,CACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}