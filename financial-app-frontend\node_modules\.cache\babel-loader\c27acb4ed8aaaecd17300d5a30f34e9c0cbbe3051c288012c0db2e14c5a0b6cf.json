{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\fiancee\\\\financial-app-frontend\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [accounts, setAccounts] = useState([]);\n  const [token, setToken] = useState(''); // Retrieve token from storage or context\n\n  const fetchAccounts = async () => {\n    try {\n      const response = await axios.get('http://127.0.0.1:8000/accounts/', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setAccounts(response.data);\n    } catch (error) {\n      console.error('Failed to fetch accounts:', error);\n    }\n  };\n  useEffect(() => {\n    if (token) fetchAccounts();\n  }, [token]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Your Accounts\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: accounts.map(account => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [\"Account ID: \", account.id, \" - Balance: $\", account.balance]\n      }, account.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 9\n  }, this);\n};\n_s(Dashboard, \"VgKHIxUGiMYF8ilkpjbIGgDchCY=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "accounts", "setAccounts", "token", "setToken", "fetchAccounts", "response", "get", "headers", "Authorization", "data", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "account", "id", "balance", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/fiancee/financial-app-frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport axios from 'axios';\r\n\r\nconst Dashboard = () => {\r\n    const [accounts, setAccounts] = useState([]);\r\n    const [token, setToken] = useState(''); // Retrieve token from storage or context\r\n\r\n    const fetchAccounts = async () => {\r\n        try {\r\n            const response = await axios.get('http://127.0.0.1:8000/accounts/', {\r\n                headers: {\r\n                    Authorization: `Bearer ${token}`,\r\n                },\r\n            });\r\n            setAccounts(response.data);\r\n        } catch (error) {\r\n            console.error('Failed to fetch accounts:', error);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (token) fetchAccounts();\r\n    }, [token]);\r\n\r\n    return (\r\n        <div>\r\n            <h2>Your Accounts</h2>\r\n            <ul>\r\n                {accounts.map((account) => (\r\n                    <li key={account.id}>Account ID: {account.id} - Balance: ${account.balance}</li>\r\n                ))}\r\n            </ul>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAExC,MAAMU,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMV,KAAK,CAACW,GAAG,CAAC,iCAAiC,EAAE;QAChEC,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK;QAClC;MACJ,CAAC,CAAC;MACFD,WAAW,CAACI,QAAQ,CAACI,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACrD;EACJ,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACZ,IAAIS,KAAK,EAAEE,aAAa,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACF,KAAK,CAAC,CAAC;EAEX,oBACIL,OAAA;IAAAe,QAAA,gBACIf,OAAA;MAAAe,QAAA,EAAI;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtBnB,OAAA;MAAAe,QAAA,EACKZ,QAAQ,CAACiB,GAAG,CAAEC,OAAO,iBAClBrB,OAAA;QAAAe,QAAA,GAAqB,cAAY,EAACM,OAAO,CAACC,EAAE,EAAC,eAAa,EAACD,OAAO,CAACE,OAAO;MAAA,GAAjEF,OAAO,CAACC,EAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA4D,CAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEd,CAAC;AAACjB,EAAA,CA/BID,SAAS;AAAAuB,EAAA,GAATvB,SAAS;AAiCf,eAAeA,SAAS;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}