{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\fiancee\\\\financial-app-frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom'; // Import useNavigate for navigation\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const navigate = useNavigate(); // Initialize navigation hook\n\n  const handleLogin = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://127.0.0.1:8000/login', {\n        username,\n        password\n      });\n      localStorage.setItem('token', response.data.access_token); // Store the token in local storage\n      navigate('/dashboard'); // Redirect to Dashboard after successful login\n    } catch (error) {\n      console.error('Error during login:', error);\n      setErrorMessage('Login failed. Please check your credentials.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleLogin,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: username,\n        onChange: e => setUsername(e.target.value),\n        placeholder: \"Username\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"password\",\n        value: password,\n        onChange: e => setPassword(e.target.value),\n        placeholder: \"Password\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: errorMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 30\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n};\n_s(Login, \"8U378PBpQ7JiNpIRa9lTFGoSkAQ=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "username", "setUsername", "password", "setPassword", "errorMessage", "setErrorMessage", "navigate", "handleLogin", "e", "preventDefault", "response", "post", "localStorage", "setItem", "data", "access_token", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/fiancee/financial-app-frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom'; // Import useNavigate for navigation\r\n\r\nconst Login = () => {\r\n    const [username, setUsername] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [errorMessage, setErrorMessage] = useState('');\r\n    const navigate = useNavigate(); // Initialize navigation hook\r\n\r\n    const handleLogin = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.post('http://127.0.0.1:8000/login', { username, password });\r\n            localStorage.setItem('token', response.data.access_token); // Store the token in local storage\r\n            navigate('/dashboard'); // Redirect to Dashboard after successful login\r\n        } catch (error) {\r\n            console.error('Error during login:', error);\r\n            setErrorMessage('Login failed. Please check your credentials.');\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <h2><PERSON>gin</h2>\r\n            <form onSubmit={handleLogin}>\r\n                <input type=\"text\" value={username} onChange={(e) => setUsername(e.target.value)} placeholder=\"Username\" required />\r\n                <input type=\"password\" value={password} onChange={(e) => setPassword(e.target.value)} placeholder=\"Password\" required />\r\n                <button type=\"submit\">Login</button>\r\n            </form>\r\n            {errorMessage && <p>{errorMessage}</p>}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAMa,QAAQ,GAAGX,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEhC,MAAMY,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,IAAI,CAAC,6BAA6B,EAAE;QAAEX,QAAQ;QAAEE;MAAS,CAAC,CAAC;MACxFU,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;MAC3DT,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOU,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CX,eAAe,CAAC,8CAA8C,CAAC;IACnE;EACJ,CAAC;EAED,oBACIR,OAAA;IAAAqB,QAAA,gBACIrB,OAAA;MAAAqB,QAAA,EAAI;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACdzB,OAAA;MAAM0B,QAAQ,EAAEhB,WAAY;MAAAW,QAAA,gBACxBrB,OAAA;QAAO2B,IAAI,EAAC,MAAM;QAACC,KAAK,EAAEzB,QAAS;QAAC0B,QAAQ,EAAGlB,CAAC,IAAKP,WAAW,CAACO,CAAC,CAACmB,MAAM,CAACF,KAAK,CAAE;QAACG,WAAW,EAAC,UAAU;QAACC,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpHzB,OAAA;QAAO2B,IAAI,EAAC,UAAU;QAACC,KAAK,EAAEvB,QAAS;QAACwB,QAAQ,EAAGlB,CAAC,IAAKL,WAAW,CAACK,CAAC,CAACmB,MAAM,CAACF,KAAK,CAAE;QAACG,WAAW,EAAC,UAAU;QAACC,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxHzB,OAAA;QAAQ2B,IAAI,EAAC,QAAQ;QAAAN,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,EACNlB,YAAY,iBAAIP,OAAA;MAAAqB,QAAA,EAAId;IAAY;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrC,CAAC;AAEd,CAAC;AAACvB,EAAA,CA7BID,KAAK;EAAA,QAIUH,WAAW;AAAA;AAAAmC,EAAA,GAJ1BhC,KAAK;AA+BX,eAAeA,KAAK;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}