{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\fiancee\\\\financial-app-frontend\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [accounts, setAccounts] = useState([]);\n  const [errorMessage, setErrorMessage] = useState('');\n  useEffect(() => {\n    const fetchAccounts = async () => {\n      const token = localStorage.getItem('token'); // Retrieve the token from local storage\n      if (!token) {\n        alert('Please log in to continue.');\n        return; // Exit if no token\n      }\n      try {\n        const response = await axios.get('http://127.0.0.1:8000/accounts/', {\n          headers: {\n            Authorization: `Bearer ${token}` // Include token in headers\n          }\n        });\n        setAccounts(response.data); // Set accounts data\n      } catch (error) {\n        console.error('Failed to fetch accounts:', error);\n        setErrorMessage('Failed to fetch accounts. Please log in again.');\n      }\n    };\n    fetchAccounts();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Your Accounts\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this), errorMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: errorMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 30\n    }, this), accounts.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: accounts.map(account => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [\"Account ID: \", account.id, \" - Balance: $\", account.balance]\n      }, account.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 25\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"No accounts found. Please create an account.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 9\n  }, this);\n};\n_s(Dashboard, \"Y3+8dPw42WnAWN9jMJ+IqPgFHeE=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "accounts", "setAccounts", "errorMessage", "setErrorMessage", "fetchAccounts", "token", "localStorage", "getItem", "alert", "response", "get", "headers", "Authorization", "data", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "account", "id", "balance", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/fiancee/financial-app-frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport axios from 'axios';\r\n\r\nconst Dashboard = () => {\r\n    const [accounts, setAccounts] = useState([]);\r\n    const [errorMessage, setErrorMessage] = useState('');\r\n\r\n    useEffect(() => {\r\n        const fetchAccounts = async () => {\r\n            const token = localStorage.getItem('token'); // Retrieve the token from local storage\r\n            if (!token) {\r\n                alert('Please log in to continue.');\r\n                return; // Exit if no token\r\n            }\r\n            try {\r\n                const response = await axios.get('http://127.0.0.1:8000/accounts/', {\r\n                    headers: {\r\n                        Authorization: `Bearer ${token}`, // Include token in headers\r\n                    },\r\n                });\r\n                setAccounts(response.data); // Set accounts data\r\n            } catch (error) {\r\n                console.error('Failed to fetch accounts:', error);\r\n                setErrorMessage('Failed to fetch accounts. Please log in again.');\r\n            }\r\n        };\r\n        fetchAccounts();\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <h2>Your Accounts</h2>\r\n            {errorMessage && <p>{errorMessage}</p>}\r\n            {accounts.length > 0 ? (\r\n                <ul>\r\n                    {accounts.map((account) => (\r\n                        <li key={account.id}>Account ID: {account.id} - Balance: ${account.balance}</li>\r\n                    ))}\r\n                </ul>\r\n            ) : (\r\n                <p>No accounts found. Please create an account.</p>\r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAEpDD,SAAS,CAAC,MAAM;IACZ,MAAMW,aAAa,GAAG,MAAAA,CAAA,KAAY;MAC9B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACF,KAAK,EAAE;QACRG,KAAK,CAAC,4BAA4B,CAAC;QACnC,OAAO,CAAC;MACZ;MACA,IAAI;QACA,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACe,GAAG,CAAC,iCAAiC,EAAE;UAChEC,OAAO,EAAE;YACLC,aAAa,EAAE,UAAUP,KAAK,EAAE,CAAE;UACtC;QACJ,CAAC,CAAC;QACFJ,WAAW,CAACQ,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDX,eAAe,CAAC,gDAAgD,CAAC;MACrE;IACJ,CAAC;IACDC,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIP,OAAA;IAAAmB,QAAA,gBACInB,OAAA;MAAAmB,QAAA,EAAI;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACrBlB,YAAY,iBAAIL,OAAA;MAAAmB,QAAA,EAAId;IAAY;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACrCpB,QAAQ,CAACqB,MAAM,GAAG,CAAC,gBAChBxB,OAAA;MAAAmB,QAAA,EACKhB,QAAQ,CAACsB,GAAG,CAAEC,OAAO,iBAClB1B,OAAA;QAAAmB,QAAA,GAAqB,cAAY,EAACO,OAAO,CAACC,EAAE,EAAC,eAAa,EAACD,OAAO,CAACE,OAAO;MAAA,GAAjEF,OAAO,CAACC,EAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA4D,CAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,gBAELvB,OAAA;MAAAmB,QAAA,EAAG;IAA4C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CACrD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACrB,EAAA,CAzCID,SAAS;AAAA4B,EAAA,GAAT5B,SAAS;AA2Cf,eAAeA,SAAS;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}