{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\fiancee\\\\financial-app-frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [token, setToken] = useState('');\n  const handleLogin = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://127.0.0.1:8000/login', {\n        username,\n        password\n      });\n      setToken(response.data.access_token);\n      alert('Login successful!');\n      // Add logic to redirect to dashboard if needed\n    } catch (error) {\n      console.error('Error during login:', error);\n      alert('<PERSON><PERSON> failed. Please check the console for details.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleLogin,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: username,\n        onChange: e => setUsername(e.target.value),\n        placeholder: \"Username\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"password\",\n        value: password,\n        onChange: e => setPassword(e.target.value),\n        placeholder: \"Password\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this), token && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Your token: \", token]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 23\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 9\n  }, this);\n};\n_s(Login, \"hN1fT4A9PZGi+YG7PE7W84HokL4=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "username", "setUsername", "password", "setPassword", "token", "setToken", "handleLogin", "e", "preventDefault", "response", "post", "data", "access_token", "alert", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/fiancee/financial-app-frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\n\r\nconst Login = () => {\r\n    const [username, setUsername] = useState('');\r\n    const [password, setPassword] = useState('');\r\n    const [token, setToken] = useState('');\r\n\r\n    const handleLogin = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.post('http://127.0.0.1:8000/login', { username, password });\r\n            setToken(response.data.access_token);\r\n            alert('Login successful!');\r\n            // Add logic to redirect to dashboard if needed\r\n        } catch (error) {\r\n            console.error('Error during login:', error);\r\n            alert('<PERSON><PERSON> failed. Please check the console for details.');\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <h2>Login</h2>\r\n            <form onSubmit={handleLogin}>\r\n                <input type=\"text\" value={username} onChange={(e) => setUsername(e.target.value)} placeholder=\"Username\" required />\r\n                <input type=\"password\" value={password} onChange={(e) => setPassword(e.target.value)} placeholder=\"Password\" required />\r\n                <button type=\"submit\">Login</button>\r\n            </form>\r\n            {token && <p>Your token: {token}</p>}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMY,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACe,IAAI,CAAC,6BAA6B,EAAE;QAAEV,QAAQ;QAAEE;MAAS,CAAC,CAAC;MACxFG,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAACC,YAAY,CAAC;MACpCC,KAAK,CAAC,mBAAmB,CAAC;MAC1B;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CD,KAAK,CAAC,qDAAqD,CAAC;IAChE;EACJ,CAAC;EAED,oBACIhB,OAAA;IAAAmB,QAAA,gBACInB,OAAA;MAAAmB,QAAA,EAAI;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACdvB,OAAA;MAAMwB,QAAQ,EAAEf,WAAY;MAAAU,QAAA,gBACxBnB,OAAA;QAAOyB,IAAI,EAAC,MAAM;QAACC,KAAK,EAAEvB,QAAS;QAACwB,QAAQ,EAAGjB,CAAC,IAAKN,WAAW,CAACM,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE;QAACG,WAAW,EAAC,UAAU;QAACC,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpHvB,OAAA;QAAOyB,IAAI,EAAC,UAAU;QAACC,KAAK,EAAErB,QAAS;QAACsB,QAAQ,EAAGjB,CAAC,IAAKJ,WAAW,CAACI,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE;QAACG,WAAW,EAAC,UAAU;QAACC,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxHvB,OAAA;QAAQyB,IAAI,EAAC,QAAQ;QAAAN,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,EACNhB,KAAK,iBAAIP,OAAA;MAAAmB,QAAA,GAAG,cAAY,EAACZ,KAAK;IAAA;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnC,CAAC;AAEd,CAAC;AAACrB,EAAA,CA7BID,KAAK;AAAA8B,EAAA,GAAL9B,KAAK;AA+BX,eAAeA,KAAK;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}