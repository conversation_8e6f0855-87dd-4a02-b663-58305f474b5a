{"ast": null, "code": "const handleSignup = async e => {\n  e.preventDefault();\n  try {\n    // Ensure this URL is pointing to your backend signup endpoint\n    const response = await axios.post('http://127.0.0.1:8000/signup', {\n      username,\n      password\n    });\n    alert(`User created: ${response.data.username}`);\n    setUsername('');\n    setPassword('');\n  } catch (error) {\n    console.error('Error during signup:', error);\n    alert('Signup failed. Please check the console for details.');\n  }\n};", "map": {"version": 3, "names": ["handleSignup", "e", "preventDefault", "response", "axios", "post", "username", "password", "alert", "data", "setUsername", "setPassword", "error", "console"], "sources": ["C:/Users/<USER>/fiancee/financial-app-frontend/src/components/Signup.js"], "sourcesContent": ["const handleSignup = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n        // Ensure this URL is pointing to your backend signup endpoint\r\n        const response = await axios.post('http://127.0.0.1:8000/signup', { username, password });\r\n        alert(`User created: ${response.data.username}`);\r\n        setUsername('');\r\n        setPassword('');\r\n    } catch (error) {\r\n        console.error('Error during signup:', error);\r\n        alert('Signup failed. Please check the console for details.');\r\n    }\r\n};"], "mappings": "AAAA,MAAMA,YAAY,GAAG,MAAOC,CAAC,IAAK;EAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;EAClB,IAAI;IACA;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACC,IAAI,CAAC,8BAA8B,EAAE;MAAEC,QAAQ;MAAEC;IAAS,CAAC,CAAC;IACzFC,KAAK,CAAC,iBAAiBL,QAAQ,CAACM,IAAI,CAACH,QAAQ,EAAE,CAAC;IAChDI,WAAW,CAAC,EAAE,CAAC;IACfC,WAAW,CAAC,EAAE,CAAC;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5CJ,KAAK,CAAC,sDAAsD,CAAC;EACjE;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}