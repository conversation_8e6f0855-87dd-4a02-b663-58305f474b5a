{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\fiancee\\\\financial-app-frontend\\\\src\\\\components\\\\Signup.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Signup = () => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const handleSignup = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://127.0.0.1:8000/signup', {\n        username,\n        password\n      });\n      alert(`User created: ${response.data.username}`);\n      setUsername('');\n      setPassword('');\n    } catch (error) {\n      console.error('Error during signup:', error);\n      alert('Signup failed. Please check the console for details.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Sign Up\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSignup,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: username,\n        onChange: e => setUsername(e.target.value),\n        placeholder: \"Username\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"password\",\n        value: password,\n        onChange: e => setPassword(e.target.value),\n        placeholder: \"Password\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Sign Up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 9\n  }, this);\n};\n_s(Signup, \"wuQOK7xaXdVz4RMrZQhWbI751Oc=\");\n_c = Signup;\nexport default Signup;\nvar _c;\n$RefreshReg$(_c, \"Signup\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "Signup", "_s", "username", "setUsername", "password", "setPassword", "handleSignup", "e", "preventDefault", "response", "post", "alert", "data", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/fiancee/financial-app-frontend/src/components/Signup.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\n\r\nconst Signup = () => {\r\n    const [username, setUsername] = useState('');\r\n    const [password, setPassword] = useState('');\r\n\r\n    const handleSignup = async (e) => {\r\n        e.preventDefault();\r\n        try {\r\n            const response = await axios.post('http://127.0.0.1:8000/signup', { username, password });\r\n            alert(`User created: ${response.data.username}`);\r\n            setUsername('');\r\n            setPassword('');\r\n        } catch (error) {\r\n            console.error('Error during signup:', error);\r\n            alert('Signup failed. Please check the console for details.');\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <h2>Sign Up</h2>\r\n            <form onSubmit={handleSignup}>\r\n                <input type=\"text\" value={username} onChange={(e) => setUsername(e.target.value)} placeholder=\"Username\" required />\r\n                <input type=\"password\" value={password} onChange={(e) => setPassword(e.target.value)} placeholder=\"Password\" required />\r\n                <button type=\"submit\">Sign Up</button>\r\n            </form>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Signup;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMU,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMZ,KAAK,CAACa,IAAI,CAAC,8BAA8B,EAAE;QAAER,QAAQ;QAAEE;MAAS,CAAC,CAAC;MACzFO,KAAK,CAAC,iBAAiBF,QAAQ,CAACG,IAAI,CAACV,QAAQ,EAAE,CAAC;MAChDC,WAAW,CAAC,EAAE,CAAC;MACfE,WAAW,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CF,KAAK,CAAC,sDAAsD,CAAC;IACjE;EACJ,CAAC;EAED,oBACIZ,OAAA;IAAAgB,QAAA,gBACIhB,OAAA;MAAAgB,QAAA,EAAI;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChBpB,OAAA;MAAMqB,QAAQ,EAAEd,YAAa;MAAAS,QAAA,gBACzBhB,OAAA;QAAOsB,IAAI,EAAC,MAAM;QAACC,KAAK,EAAEpB,QAAS;QAACqB,QAAQ,EAAGhB,CAAC,IAAKJ,WAAW,CAACI,CAAC,CAACiB,MAAM,CAACF,KAAK,CAAE;QAACG,WAAW,EAAC,UAAU;QAACC,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpHpB,OAAA;QAAOsB,IAAI,EAAC,UAAU;QAACC,KAAK,EAAElB,QAAS;QAACmB,QAAQ,EAAGhB,CAAC,IAAKF,WAAW,CAACE,CAAC,CAACiB,MAAM,CAACF,KAAK,CAAE;QAACG,WAAW,EAAC,UAAU;QAACC,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxHpB,OAAA;QAAQsB,IAAI,EAAC,QAAQ;QAAAN,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAAClB,EAAA,CA3BID,MAAM;AAAA2B,EAAA,GAAN3B,MAAM;AA6BZ,eAAeA,MAAM;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}