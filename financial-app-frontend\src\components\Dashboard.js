import React, { useEffect, useState } from 'react';
import axios from 'axios';

const Dashboard = () => {
    const [accounts, setAccounts] = useState([]);
    const [errorMessage, setErrorMessage] = useState('');

    useEffect(() => {
        const fetchAccounts = async () => {
            const token = localStorage.getItem('token'); // Retrieve the token from local storage
            if (!token) {
                alert('Please log in to continue.');
                return; // Exit if no token
            }
            try {
                const response = await axios.get('http://127.0.0.1:8000/accounts/', {
                    headers: {
                        Authorization: `Bearer ${token}`, // Include token in headers
                    },
                });
                setAccounts(response.data); // Set accounts data
            } catch (error) {
                console.error('Failed to fetch accounts:', error);
                setErrorMessage('Failed to fetch accounts. Please log in again.');
            }
        };
        fetchAccounts();
    }, []);

    return (
        <div>
            <h2>Your Accounts</h2>
            {errorMessage && <p>{errorMessage}</p>}
            {accounts.length > 0 ? (
                <ul>
                    {accounts.map((account) => (
                        <li key={account.id}>Account ID: {account.id} - Balance: ${account.balance}</li>
                    ))}
                </ul>
            ) : (
                <p>No accounts found. Please create an account.</p>
            )}
        </div>
    );
};

export default Dashboard;